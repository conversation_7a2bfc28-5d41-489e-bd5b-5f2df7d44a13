// 云函数入口文件
const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()
const _ = db.command

// 云函数入口函数
exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext()
  
  try {
    const { action, data, flockId, page = 1, limit = 20 } = event
    const { openid } = wxContext
    
    if (!openid) {
      return {
        success: false,
        message: '用户未登录',
        code: 'NOT_AUTHENTICATED'
      }
    }
    
    // 获取用户信息
    const userQuery = await db.collection('users').where({
      openid: openid
    }).get()
    
    if (userQuery.data.length === 0) {
      return {
        success: false,
        message: '用户不存在',
        code: 'USER_NOT_FOUND'
      }
    }
    
    const user = userQuery.data[0]
    
    switch (action) {
      case 'list':
        return await getFlockList(user, page, limit)
      case 'detail':
        return await getFlockDetail(user, flockId)
      case 'create':
        return await createFlock(user, data)
      case 'update':
        return await updateFlock(user, flockId, data)
      case 'delete':
        return await deleteFlock(user, flockId)
      default:
        return {
          success: false,
          message: '不支持的操作',
          code: 'UNSUPPORTED_ACTION'
        }
    }
    
  } catch (error) {
    console.error('鹅群管理操作失败:', error)
    return {
      success: false,
      message: '操作失败',
      code: 'OPERATION_ERROR',
      error: error.message
    }
  }
}

// 获取鹅群列表
async function getFlockList(user, page, limit) {
  const skip = (page - 1) * limit
  
  const query = db.collection('flocks').where({
    tenant_id: user.tenant_id,
    user_id: user._id
  })
  
  const countResult = await query.count()
  const listResult = await query
    .orderBy('created_at', 'desc')
    .skip(skip)
    .limit(limit)
    .get()
  
  return {
    success: true,
    message: '获取鹅群列表成功',
    data: {
      list: listResult.data,
      pagination: {
        page: page,
        limit: limit,
        total: countResult.total,
        pages: Math.ceil(countResult.total / limit)
      }
    }
  }
}

// 获取鹅群详情
async function getFlockDetail(user, flockId) {
  if (!flockId) {
    return {
      success: false,
      message: '鹅群ID不能为空',
      code: 'FLOCK_ID_REQUIRED'
    }
  }
  
  const flockResult = await db.collection('flocks').doc(flockId).get()
  
  if (!flockResult.data) {
    return {
      success: false,
      message: '鹅群不存在',
      code: 'FLOCK_NOT_FOUND'
    }
  }
  
  const flock = flockResult.data
  
  // 检查权限
  if (flock.tenant_id !== user.tenant_id || flock.user_id !== user._id) {
    return {
      success: false,
      message: '无权限访问该鹅群',
      code: 'ACCESS_DENIED'
    }
  }
  
  return {
    success: true,
    message: '获取鹅群详情成功',
    data: flock
  }
}

// 创建鹅群
async function createFlock(user, data) {
  const { name, breed, count, age, location } = data
  
  if (!name || !breed || !count) {
    return {
      success: false,
      message: '鹅群名称、品种和数量不能为空',
      code: 'REQUIRED_FIELDS_MISSING'
    }
  }
  
  const flockData = {
    tenant_id: user.tenant_id,
    user_id: user._id,
    name: name,
    breed: breed,
    count: parseInt(count),
    age: parseInt(age) || 0,
    health_status: 'healthy',
    location: location || '',
    created_at: new Date(),
    updated_at: new Date()
  }
  
  const result = await db.collection('flocks').add({
    data: flockData
  })
  
  return {
    success: true,
    message: '创建鹅群成功',
    data: {
      id: result._id,
      ...flockData
    }
  }
}

// 更新鹅群
async function updateFlock(user, flockId, data) {
  if (!flockId) {
    return {
      success: false,
      message: '鹅群ID不能为空',
      code: 'FLOCK_ID_REQUIRED'
    }
  }
  
  // 检查鹅群是否存在和权限
  const flockResult = await db.collection('flocks').doc(flockId).get()
  
  if (!flockResult.data) {
    return {
      success: false,
      message: '鹅群不存在',
      code: 'FLOCK_NOT_FOUND'
    }
  }
  
  const flock = flockResult.data
  
  if (flock.tenant_id !== user.tenant_id || flock.user_id !== user._id) {
    return {
      success: false,
      message: '无权限修改该鹅群',
      code: 'ACCESS_DENIED'
    }
  }
  
  const updateData = {
    ...data,
    updated_at: new Date()
  }
  
  // 移除不允许更新的字段
  delete updateData.tenant_id
  delete updateData.user_id
  delete updateData.created_at
  
  await db.collection('flocks').doc(flockId).update({
    data: updateData
  })
  
  return {
    success: true,
    message: '更新鹅群成功',
    data: {
      id: flockId,
      ...updateData
    }
  }
}

// 删除鹅群
async function deleteFlock(user, flockId) {
  if (!flockId) {
    return {
      success: false,
      message: '鹅群ID不能为空',
      code: 'FLOCK_ID_REQUIRED'
    }
  }
  
  // 检查鹅群是否存在和权限
  const flockResult = await db.collection('flocks').doc(flockId).get()
  
  if (!flockResult.data) {
    return {
      success: false,
      message: '鹅群不存在',
      code: 'FLOCK_NOT_FOUND'
    }
  }
  
  const flock = flockResult.data
  
  if (flock.tenant_id !== user.tenant_id || flock.user_id !== user._id) {
    return {
      success: false,
      message: '无权限删除该鹅群',
      code: 'ACCESS_DENIED'
    }
  }
  
  await db.collection('flocks').doc(flockId).remove()
  
  return {
    success: true,
    message: '删除鹅群成功',
    data: {
      id: flockId
    }
  }
}
