{"read": "doc._openid == auth.openid", "write": "doc._openid == auth.openid", "collections": {"users": {"read": "auth != null && (resource.data.openid == auth.openid || auth.role == 'admin' || auth.role == 'super_admin')", "write": "auth != null && resource.data.openid == auth.openid"}, "tenants": {"read": "auth != null && (auth.role == 'admin' || auth.role == 'super_admin')", "write": "auth != null && auth.role == 'super_admin'"}, "flocks": {"read": "auth != null && resource.data.tenant_id == auth.tenant_id", "write": "auth != null && resource.data.tenant_id == auth.tenant_id && (resource.data.user_id == auth.uid || auth.role == 'admin')"}, "health_records": {"read": "auth != null && resource.data.tenant_id == auth.tenant_id", "write": "auth != null && resource.data.tenant_id == auth.tenant_id && (resource.data.user_id == auth.uid || auth.role == 'admin')"}, "production_records": {"read": "auth != null && resource.data.tenant_id == auth.tenant_id", "write": "auth != null && resource.data.tenant_id == auth.tenant_id && (resource.data.user_id == auth.uid || auth.role == 'admin')"}, "products": {"read": "auth != null && resource.data.tenant_id == auth.tenant_id", "write": "auth != null && resource.data.tenant_id == auth.tenant_id && (auth.role == 'admin' || auth.permissions.indexOf('product_manage') >= 0)"}, "orders": {"read": "auth != null && resource.data.tenant_id == auth.tenant_id && (resource.data.user_id == auth.uid || auth.role == 'admin')", "write": "auth != null && resource.data.tenant_id == auth.tenant_id && resource.data.user_id == auth.uid"}, "oa_applications": {"read": "auth != null && resource.data.tenant_id == auth.tenant_id && (resource.data.user_id == auth.uid || resource.data.current_approver_id == auth.uid || auth.role == 'admin')", "write": "auth != null && resource.data.tenant_id == auth.tenant_id && (resource.data.user_id == auth.uid || resource.data.current_approver_id == auth.uid || auth.role == 'admin')"}, "system_config": {"read": "auth != null && (resource.data.tenant_id == auth.tenant_id || resource.data.is_public == true)", "write": "auth != null && auth.role == 'admin'"}, "announcements": {"read": "auth != null && (resource.data.tenant_id == auth.tenant_id || resource.data.tenant_id == null)", "write": "auth != null && auth.role == 'admin'"}, "knowledge_base": {"read": "auth != null && (resource.data.tenant_id == auth.tenant_id || resource.data.tenant_id == null)", "write": "auth != null && (auth.role == 'admin' || auth.permissions.indexOf('knowledge_manage') >= 0)"}}}